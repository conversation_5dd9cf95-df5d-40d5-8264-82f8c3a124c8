package com.oneplatform.backendcore.customer.domain.service

import com.oneplatform.backendcore.core.exceptions.GraphQLErrorCode
import com.oneplatform.backendcore.core.integration.mapper.toDomain
import com.oneplatform.backendcore.customer.domain.model.customer.BusinessCustomer
import com.oneplatform.backendcore.customer.domain.model.customer.BusinessCustomerDetails
import com.oneplatform.backendcore.customer.domain.model.customer.Customer
import com.oneplatform.backendcore.customer.domain.model.customer.IndividualCustomer
import com.oneplatform.backendcore.customer.domain.model.customer.IndividualCustomerDetails
import com.oneplatform.backendcore.customer.domain.repository.BusinessCustomerRepository
import com.oneplatform.backendcore.customer.domain.repository.IndividualCustomerRepository
import com.oneplatform.backendcore.customer.exception.CustomerDomainException
import com.oneplatform.backendcore.types.*
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.stereotype.Service

private val log = KotlinLogging.logger {}

@Service
class CustomerDetailsDomainService(
    private val individualCustomerRepository: IndividualCustomerRepository,
    private val businessCustomerRepository: BusinessCustomerRepository
) {

    fun createIndividualCustomer(
        customer: IndividualCustomer,
        createdNotes: Notes?,
        createdDocuments: List<Document>,
        createdCustomTags: List<CustomTag>
    ): IndividualCustomer {

        val individualCustomerForSaving = IndividualCustomer(
            companyId = customer.companyId,
            details = IndividualCustomerDetails(
                basicDetails = customer.details.basicDetails,
                documents = createdDocuments.map { it.toDomain() },
                notes = createdNotes?.toDomain(),
                address = customer.details.address,
                customTags = createdCustomTags.map { it.toDomain() },
                assignments = customer.details.assignments
            ),
            status = CustomerStatus.ACTIVE,
            stage = customer.stage,
        )

        return individualCustomerRepository.save(individualCustomerForSaving)
    }


    fun createBusinessCustomer(
        customer: BusinessCustomer,
        createdNotes: Notes?,
        createdDocuments: List<Document>,
        createdCustomTags: List<CustomTag>,
    ): BusinessCustomer {
        val businessCustomerForSaving = BusinessCustomer(
            companyId = customer.companyId,
            details = BusinessCustomerDetails(
                basicDetails = customer.details.basicDetails,
                documents = createdDocuments.map { it.toDomain() },
                notes = createdNotes?.toDomain(),
                address = customer.details.address,
                customTags = createdCustomTags.map { it.toDomain() },
                assignments = customer.details.assignments
            ),
            status = CustomerStatus.ACTIVE,
            stage = customer.stage,
        )

        return businessCustomerRepository.save(businessCustomerForSaving)
    }

    fun getCustomers(
        filter: GetCustomersQueryFilterInput,
        companyId: String
    ): List<Customer> {
        val allResults = businessCustomerRepository.findAllWithSpec(filter, companyId) + individualCustomerRepository.findAllWithSpec(filter, companyId)
        log.info { "Fetched results=${allResults.count()} for filter=${filter}"  }
        return allResults
    }

    fun getCustomerById(id: String): Customer? {
        log.info { "Finding customer by id=$id" }

        val businessCustomer = businessCustomerRepository.findById(id)
        if (businessCustomer != null) {
            log.info { "Found business customer with id=$id" }
            return businessCustomer
        }

        val individualCustomer = individualCustomerRepository.findById(id)
        if (individualCustomer != null) {
            log.info { "Found individual customer with id=$id" }
            return individualCustomer
        }

        log.info { "No customer found with id=$id" }
        return null
    }
}