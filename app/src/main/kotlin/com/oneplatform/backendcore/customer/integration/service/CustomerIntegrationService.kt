package com.oneplatform.backendcore.customer.integration.service

import com.oneplatform.backendcore.company.client.`interface`.CompanyClientAdapter
import com.oneplatform.backendcore.company.client.`interface`.CompanyUserClientAdapter
import com.oneplatform.backendcore.company.exception.CompanyNotFoundException
import com.oneplatform.backendcore.core.auth.AuthenticatedUser
import com.oneplatform.backendcore.core.domain.model.User
import com.oneplatform.backendcore.core.exceptions.GraphQLErrorCode
import com.oneplatform.backendcore.customer.domain.model.customer.BusinessCustomer
import com.oneplatform.backendcore.customer.domain.model.customer.Customer
import com.oneplatform.backendcore.customer.domain.model.customer.IndividualCustomer
import com.oneplatform.backendcore.customer.domain.request.CreateCustomerDomainReq
import com.oneplatform.backendcore.customer.domain.service.CustomerDomainService
import com.oneplatform.backendcore.customer.integration.createBusinessCustomerDetails
import com.oneplatform.backendcore.customer.integration.createIndividualCustomerDetails
import com.oneplatform.backendcore.types.*
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.stereotype.Service


private val log = KotlinLogging.logger {}

@Service
class CustomerIntegrationService(
    private val customerDomainService: CustomerDomainService,
    private val companyClientAdapter: CompanyClientAdapter,
    private val companyUserClientAdapter: CompanyUserClientAdapter
) {

    fun createCustomer(user: User, input: CreateCustomerInput): Customer {
        val companyUser = companyUserClientAdapter.getCompanyUserForUserID(user.id)
        val company = companyClientAdapter.getCompanyForID(companyUser.company.id)

        log.info { "Creating customer for company=${company.id} by companyUser=${companyUser.id}" }
        return customerDomainService.createCustomer(
            domainReq = CreateCustomerDomainReq(
                individualCustomer = createIndividualCustomer(input, company.id),
                businessCustomer = createBusinessCustomer(input, company.id),
                company = company,
                companyUser = companyUser
            )
        )
    }

    fun getCustomerForFilters(filters: GetCustomersQueryFilterInput, authUser: AuthenticatedUser): List<Customer> {
        log.info { "Getting all companies for filters=$filters and authUserId:${authUser.getUser()!!.id}" }
        val companyUser = companyUserClientAdapter.getCompanyUserForUserID(authUser.getUser()!!.id)
        val company = companyClientAdapter.getCompanyForID(companyUser.company.id)

        if (company.status == CompanyStatus.INACTIVE) {
            log.error { "Company is inactive for companyID=${company.id}" }
            throw CompanyNotFoundException(
                "Company is inactive for companyID=${company.id}",
                graphQLErrorCode = GraphQLErrorCode.COMPANY_IS_INACTIVE
            )
        }

        return customerDomainService.getCustomersForCompanyIdWithBasicDetails(filters, company.id)

    }

    fun getCustomer(customerId: String, authUser: AuthenticatedUser): Customer? {
        val user = authUser.getUser()
        if (user == null) {
            log.error { "User is not authenticated" }
            throw IllegalStateException("User is not authenticated")
        }

        log.info { "Getting customer with id=$customerId for authUserId=${user.id}" }
        val companyUser = companyUserClientAdapter.getCompanyUserForUserID(user.id)
        val company = companyClientAdapter.getCompanyForID(companyUser.company.id)

        if (company.status == CompanyStatus.INACTIVE) {
            log.error { "Company is inactive for companyID=${company.id}" }
            throw CompanyNotFoundException(
                "Company is inactive for companyID=${company.id}",
                graphQLErrorCode = GraphQLErrorCode.COMPANY_IS_INACTIVE
            )
        }

        val customer = customerDomainService.getCustomerById(customerId)
        if (customer == null) {
            log.info { "Customer not found with id=$customerId" }
            return null
        }

        if (customer.companyId != company.id) {
            log.error { "Customer with id=$customerId does not belong to company=${company.id}" }
            throw CompanyNotFoundException(
                "Customer with id=$customerId does not belong to company=${company.id}",
                graphQLErrorCode = GraphQLErrorCode.COMPANY_IS_INACTIVE
            )
        }

        log.info { "Successfully retrieved customer with id=$customerId for company=${company.id}" }
        return customer
    }

    private fun createIndividualCustomer(input: CreateCustomerInput, companyId: String): IndividualCustomer? {
        val customerType = input.customerType
        if (customerType == CustomerType.BUSINESS) {
            log.info { "Customer type is business, not creating individual customer" }
            return null
        }
        log.info { "Creating a customer details request for individual customer" }
        return IndividualCustomer(
            details = input.createIndividualCustomerDetails(),
            companyId = companyId,
            stage = input.customerStage
        )
    }


    private fun createBusinessCustomer(input: CreateCustomerInput, companyId: String): BusinessCustomer? {
        val customerType = input.customerType
        if (customerType == CustomerType.INDIVIDUAL) {
            log.info { "Customer type is individual, not creating business customer" }
            return null
        }
        log.info { "Creating a customer details request for business customer" }
        return BusinessCustomer(
            details = input.createBusinessCustomerDetails(),
            companyId = companyId,
            stage = input.customerStage,
            status = CustomerStatus.ACTIVE,
        )
    }
}